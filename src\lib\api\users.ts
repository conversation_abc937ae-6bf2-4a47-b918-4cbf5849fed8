import { PrismaClient } from '@prisma/client';
import { 
  UpdateUserSchema, 
  ChangePasswordSchema,
  type UpdateUserInput, 
  type ChangePasswordInput,
  type UserResponse,
  type ApiResponse 
} from '@/lib/validators/auth';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * Update user profile
 */
export async function updateUser(userId: string, input: UpdateUserInput): Promise<ApiResponse<UserResponse>> {
  try {
    // Validate input
    const validatedInput = UpdateUserSchema.parse(input);

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      return {
        success: false,
        message: 'User not found',
      };
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...validatedInput,
        updatedAt: new Date(),
      },
    });

    // Return user without password
    const userResponse: UserResponse = {
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.firstName,
      lastName: updatedUser.lastName,
      phone: updatedUser.phone,
      role: updatedUser.role,
      isActive: updatedUser.isActive,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      emailVerified: updatedUser.emailVerified,
      image: updatedUser.image,
    };

    return {
      success: true,
      message: 'User updated successfully',
      data: userResponse,
    };
  } catch (error) {
    console.error('Update user error:', error);
    
    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred while updating user',
    };
  }
}

/**
 * Change user password
 */
export async function changePassword(userId: string, input: ChangePasswordInput): Promise<ApiResponse> {
  try {
    // Validate input
    const validatedInput = ChangePasswordSchema.parse(input);

    // Get user with current password
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found',
      };
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(validatedInput.currentPassword, user.password);

    if (!isCurrentPasswordValid) {
      return {
        success: false,
        message: 'Current password is incorrect',
      };
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(validatedInput.newPassword, 12);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date(),
      },
    });

    return {
      success: true,
      message: 'Password changed successfully',
    };
  } catch (error) {
    console.error('Change password error:', error);
    
    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred while changing password',
    };
  }
}

/**
 * Get all users (admin only)
 */
export async function getAllUsers(page: number = 1, limit: number = 10): Promise<ApiResponse<{ users: UserResponse[], total: number, page: number, limit: number }>> {
  try {
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count(),
    ]);

    const userResponses: UserResponse[] = users.map(user => ({
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      emailVerified: user.emailVerified,
      image: user.image,
    }));

    return {
      success: true,
      message: 'Users retrieved successfully',
      data: {
        users: userResponses,
        total,
        page,
        limit,
      },
    };
  } catch (error) {
    console.error('Get all users error:', error);
    
    return {
      success: false,
      message: 'An unexpected error occurred while retrieving users',
    };
  }
}

/**
 * Deactivate user account
 */
export async function deactivateUser(userId: string): Promise<ApiResponse> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found',
      };
    }

    await prisma.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    return {
      success: true,
      message: 'User account deactivated successfully',
    };
  } catch (error) {
    console.error('Deactivate user error:', error);
    
    return {
      success: false,
      message: 'An unexpected error occurred while deactivating user',
    };
  }
}

/**
 * Activate user account
 */
export async function activateUser(userId: string): Promise<ApiResponse> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found',
      };
    }

    await prisma.user.update({
      where: { id: userId },
      data: {
        isActive: true,
        updatedAt: new Date(),
      },
    });

    return {
      success: true,
      message: 'User account activated successfully',
    };
  } catch (error) {
    console.error('Activate user error:', error);
    
    return {
      success: false,
      message: 'An unexpected error occurred while activating user',
    };
  }
}
