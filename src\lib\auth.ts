import NextAuth from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { PrismaAdapter } from '@auth/prisma-adapter';
import { PrismaClient } from '@prisma/client';
import { authenticateUser } from '@/lib/api/auth';
import { LoginUserSchema } from '@/lib/validators/auth';

const prisma = new PrismaClient();

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: 'jwt',
  },
  pages: {
    signIn: '/auth/login',
    signUp: '/auth/register',
  },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { 
          label: 'Email', 
          type: 'email',
          placeholder: 'Enter your email'
        },
        password: { 
          label: 'Password', 
          type: 'password',
          placeholder: 'Enter your password'
        },
      },
      async authorize(credentials) {
        try {
          // Validate credentials
          const validatedCredentials = LoginUserSchema.parse(credentials);
          
          // Authenticate user
          const result = await authenticateUser(validatedCredentials);
          
          if (!result.success || !result.data) {
            return null;
          }

          const user = result.data;
          
          return {
            id: user.id,
            email: user.email,
            name: `${user.firstName} ${user.lastName}`,
            image: user.image,
            role: user.role,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            isActive: user.isActive,
          };
        } catch (error) {
          console.error('Authorization error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Persist user data in the token
      if (user) {
        token.role = user.role;
        token.firstName = user.firstName;
        token.lastName = user.lastName;
        token.phone = user.phone;
        token.isActive = user.isActive;
      }
      return token;
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.phone = token.phone as string | null;
        session.user.isActive = token.isActive as boolean;
      }
      return session;
    },
  },
  events: {
    async signIn({ user, account, profile }) {
      console.log('User signed in:', { userId: user.id, email: user.email });
    },
    async signOut({ session, token }) {
      console.log('User signed out:', { userId: token?.sub });
    },
  },
  debug: process.env.NODE_ENV === 'development',
});

// Type declarations for NextAuth
declare module 'next-auth' {
  interface User {
    role: string;
    firstName: string;
    lastName: string;
    phone: string | null;
    isActive: boolean;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      image?: string | null;
      role: string;
      firstName: string;
      lastName: string;
      phone: string | null;
      isActive: boolean;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: string;
    firstName: string;
    lastName: string;
    phone: string | null;
    isActive: boolean;
  }
}
