import bcrypt from 'bcryptjs';
import { PrismaClient } from '@prisma/client';
import { 
  RegisterUserSchema, 
  LoginUserSchema, 
  type RegisterUserInput, 
  type LoginUserInput,
  type UserResponse,
  type ApiResponse 
} from '@/lib/validators/auth';

const prisma = new PrismaClient();

/**
 * Register a new user
 */
export async function registerUser(input: RegisterUserInput): Promise<ApiResponse<UserResponse>> {
  try {
    // Validate input
    const validatedInput = RegisterUserSchema.parse(input);
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedInput.email.toLowerCase() },
    });

    if (existingUser) {
      return {
        success: false,
        message: 'A user with this email already exists',
      };
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedInput.password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: validatedInput.email.toLowerCase(),
        password: hashedPassword,
        firstName: validatedInput.firstName,
        lastName: validatedInput.lastName,
        phone: validatedInput.phone || null,
        role: validatedInput.role,
      },
    });

    // Return user without password
    const userResponse: UserResponse = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      emailVerified: user.emailVerified,
      image: user.image,
    };

    return {
      success: true,
      message: 'User registered successfully',
      data: userResponse,
    };
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred during registration',
    };
  }
}

/**
 * Authenticate user credentials
 */
export async function authenticateUser(input: LoginUserInput): Promise<ApiResponse<UserResponse>> {
  try {
    // Validate input
    const validatedInput = LoginUserSchema.parse(input);

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: validatedInput.email.toLowerCase() },
    });

    if (!user) {
      return {
        success: false,
        message: 'Invalid email or password',
      };
    }

    // Check if user is active
    if (!user.isActive) {
      return {
        success: false,
        message: 'Your account has been deactivated. Please contact support.',
      };
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(validatedInput.password, user.password);

    if (!isPasswordValid) {
      return {
        success: false,
        message: 'Invalid email or password',
      };
    }

    // Return user without password
    const userResponse: UserResponse = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      emailVerified: user.emailVerified,
      image: user.image,
    };

    return {
      success: true,
      message: 'Authentication successful',
      data: userResponse,
    };
  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred during authentication',
    };
  }
}

/**
 * Get user by ID
 */
export async function getUserById(userId: string): Promise<ApiResponse<UserResponse>> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found',
      };
    }

    const userResponse: UserResponse = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      emailVerified: user.emailVerified,
      image: user.image,
    };

    return {
      success: true,
      message: 'User retrieved successfully',
      data: userResponse,
    };
  } catch (error) {
    console.error('Get user error:', error);
    
    return {
      success: false,
      message: 'An unexpected error occurred while retrieving user',
    };
  }
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<ApiResponse<UserResponse>> {
  try {
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found',
      };
    }

    const userResponse: UserResponse = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      emailVerified: user.emailVerified,
      image: user.image,
    };

    return {
      success: true,
      message: 'User retrieved successfully',
      data: userResponse,
    };
  } catch (error) {
    console.error('Get user by email error:', error);
    
    return {
      success: false,
      message: 'An unexpected error occurred while retrieving user',
    };
  }
}

// Clean up Prisma connection
export async function disconnectPrisma() {
  await prisma.$disconnect();
}
